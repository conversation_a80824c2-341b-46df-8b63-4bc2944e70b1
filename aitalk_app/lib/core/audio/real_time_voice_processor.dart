import 'package:flutter/material.dart';
import 'voice_message_processor.dart';
import 'call_audio_session_manager.dart';

/// 实时通话语音处理器
///
/// 简化实现：直接使用现有的VoiceMessageProcessor，但修改其发送逻辑
class RealTimeVoiceProcessor {
  RealTimeVoiceProcessor._();

  static final RealTimeVoiceProcessor instance = RealTimeVoiceProcessor._();

  VoiceMessageProcessor? _voiceProcessor;
  bool _isRecording = false;

  /// 开始实时通话录音
  Future<void> startRecording() async {
    if (_isRecording) {
      debugPrint('⚠️ 实时通话录音已在进行中');
      return;
    }

    try {
      debugPrint('🎤 开始实时通话录音');

      // 首先配置通话模式的音频会话（听筒模式，避免外放）
      final audioConfigured = await CallAudioSessionManager.instance
          .configureForCall();
      if (!audioConfigured) {
        debugPrint('❌ 音频会话配置失败，无法开始通话录音');
        throw Exception('音频会话配置失败');
      }

      // 如果语音处理器不存在，创建新实例
      _voiceProcessor ??= await VoiceMessageProcessor.create();

      // 开始流式录音（这会使用PTT的发送逻辑，但我们暂时接受这个限制）
      await _voiceProcessor!.startStreamingRecording(autoPlay: false);
      _isRecording = true;

      debugPrint('✅ 实时通话录音已开始（听筒模式，避免外放反馈）');
    } catch (e) {
      debugPrint('❌ 开始实时通话录音失败: $e');
      _isRecording = false;
      // 如果录音失败，恢复正常音频模式
      await CallAudioSessionManager.instance.restoreNormalMode();
      rethrow;
    }
  }

  /// 停止实时通话录音（用于静音）
  Future<void> stopRecording() async {
    if (!_isRecording) {
      debugPrint('⚠️ 实时通话录音未在进行中');
      return;
    }

    try {
      debugPrint('🎤 停止实时通话录音');

      _isRecording = false;

      // 停止语音处理器，但不销毁实例（用于静音功能）
      await _voiceProcessor?.stopStreamingRecording();

      debugPrint('✅ 实时通话录音已停止');
    } catch (e) {
      debugPrint('❌ 停止实时通话录音失败: $e');
    }
  }

  /// 完全停止并释放资源（用于挂断通话）
  Future<void> dispose() async {
    try {
      debugPrint('🎤 释放实时通话录音资源');

      _isRecording = false;

      // 停止并释放语音处理器 - 使用立即停止方法，不发送剩余数据
      await _voiceProcessor?.stopStreamingRecordingImmediately();
      _voiceProcessor = null;

      // 恢复正常音频模式
      await CallAudioSessionManager.instance.restoreNormalMode();

      debugPrint('✅ 实时通话录音资源已释放，音频模式已恢复');
    } catch (e) {
      debugPrint('❌ 释放实时通话录音资源失败: $e');
    }
  }

  /// 检查是否正在录音
  bool get isRecording => _isRecording;
}
