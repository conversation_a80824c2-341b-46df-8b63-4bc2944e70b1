import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:typed_data';
import 'package:record/record.dart';

import 'call_audio_session_manager.dart';
import 'voice_activity_detector.dart';
import 'audio_codec.dart';
import 'melp_codec.dart';
import 'multi_frame_voice_assembler.dart';
import '../bluetooth/bluetooth_manager.dart';
import '../device/device_manager.dart';
import '../protocol/tk8620_request_sender.dart';

/// 实时通话语音处理器
///
/// 集成语音活动检测（VAD），只在检测到有效语音时进行编码和发送
class RealTimeVoiceProcessor {
  RealTimeVoiceProcessor._();

  static final RealTimeVoiceProcessor instance = RealTimeVoiceProcessor._();

  // 音频录制和编码相关
  final _recorder = AudioRecorder();
  AudioCodec? _codec;
  StreamSubscription<Uint8List>? _recordStreamSub;

  // VAD和缓冲相关
  final _vad = VoiceActivityDetector.instance;
  final List<int> _pcmBuffer = [];
  final List<Uint8List> _encodedFrameBuffer = [];
  int _frameCount = 0; // 用于调试的帧计数器

  // 状态管理
  bool _isRecording = false;
  bool _isVoiceTransmitting = false; // 是否正在传输语音
  int _currentSrcId = 0x01;

  // 临时调试模式：绕过VAD直接发送所有音频
  static const bool _debugBypassVAD = true;

  // 音频参数常量
  static const int _sampleRate = 8000;
  static const int _channels = 1;
  static const int _frameSamples = 540; // MELP 1.2kbps frame size
  static const int _framesPerSend = 10;

  /// 开始实时通话录音（基于VAD）
  Future<void> startRecording() async {
    if (_isRecording) {
      debugPrint('⚠️ 实时通话录音已在进行中');
      return;
    }

    try {
      debugPrint('🎤 开始实时通话录音（集成VAD）');

      // 首先配置通话模式的音频会话（听筒模式，避免外放）
      final audioConfigured = await CallAudioSessionManager.instance
          .configureForCall();
      if (!audioConfigured) {
        debugPrint('❌ 音频会话配置失败，无法开始通话录音');
        throw Exception('音频会话配置失败');
      }

      // 初始化编解码器
      if (_codec == null) {
        _codec = MelpCodecFactory.create1200();
        await (_codec as MelpAudioCodec).init();
      }

      // 重置VAD状态并设置为高敏感度
      _vad.reset();
      _vad.setSensitivity(0.7); // 设置为较高敏感度

      // 获取当前设备ID
      _currentSrcId = 0x01;
      final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
      if (deviceIdHex != null && deviceIdHex.startsWith('0x')) {
        try {
          _currentSrcId = int.parse(deviceIdHex.substring(2), radix: 16) & 0xFF;
        } catch (_) {}
      }

      // 检查录音权限
      final hasPermission = await _recorder.hasPermission();
      if (!hasPermission) {
        debugPrint('❌ 录音权限被拒绝');
        throw Exception('录音权限被拒绝');
      }

      // 开始录音流
      final stream = await _recorder.startStream(
        const RecordConfig(
          encoder: AudioEncoder.pcm16bits,
          sampleRate: _sampleRate,
          numChannels: _channels,
        ),
      );

      _isRecording = true;
      _isVoiceTransmitting = false;

      // 监听录音流并进行VAD处理
      _recordStreamSub = stream.listen(_processAudioData);

      debugPrint('✅ 实时通话录音已开始（听筒模式 + VAD）');
    } catch (e) {
      debugPrint('❌ 开始实时通话录音失败: $e');
      _isRecording = false;
      await CallAudioSessionManager.instance.restoreNormalMode();
      rethrow;
    }
  }

  /// 音频数据处理方法（集成VAD）
  void _processAudioData(Uint8List data) async {
    if (!_isRecording) return;

    // 将字节数据转为 Int16 采样点并放入缓冲区
    final samples = _convertBytesToInt16List(data);
    _pcmBuffer.addAll(samples);

    // 按帧大小处理数据
    while (_pcmBuffer.length >= _frameSamples) {
      final frameSamples = _pcmBuffer.sublist(0, _frameSamples);
      _pcmBuffer.removeRange(0, _frameSamples);

      final frame = Int16List.fromList(frameSamples);

      // VAD检测（或调试绕过模式）
      final hasVoice = _debugBypassVAD ? true : _vad.detectVoiceActivity(frame);

      // 临时调试：每50帧输出一次VAD状态
      _frameCount++;
      if (_frameCount % 50 == 0) {
        debugPrint('🎤 VAD状态检查: hasVoice=$hasVoice, isVoiceActive=${_vad.isVoiceActive}, isTransmitting=$_isVoiceTransmitting, 绕过VAD=$_debugBypassVAD');
      }

      if (hasVoice) {
        // 检测到语音，进行编码和发送
        try {
          final packet = _codec!.encode(frame);
          _encodedFrameBuffer.add(packet);

          // 标记正在传输语音
          if (!_isVoiceTransmitting) {
            _isVoiceTransmitting = true;
            debugPrint('🎤 开始传输语音');
          }

          // 达到发送阈值时批量发送
          if (_encodedFrameBuffer.length >= _framesPerSend) {
            await _sendEncodedFrames();
          }
        } catch (e) {
          debugPrint('❌ 语音编码失败: $e');
        }
      } else if (_isVoiceTransmitting) {
        // 语音结束，发送剩余数据
        if (_encodedFrameBuffer.isNotEmpty) {
          await _sendEncodedFrames();
        }
        _isVoiceTransmitting = false;
        debugPrint('🎤 语音传输结束');
      }
    }
  }

  /// 停止实时通话录音（用于静音）
  Future<void> stopRecording() async {
    if (!_isRecording) {
      debugPrint('⚠️ 实时通话录音未在进行中');
      return;
    }

    try {
      debugPrint('🎤 停止实时通话录音');

      _isRecording = false;
      await _recordStreamSub?.cancel();
      _recordStreamSub = null;
      await _recorder.stop();

      // 发送剩余的编码数据
      if (_encodedFrameBuffer.isNotEmpty) {
        await _sendEncodedFrames();
      }

      // 清空缓冲区
      _pcmBuffer.clear();
      _encodedFrameBuffer.clear();
      _isVoiceTransmitting = false;

      debugPrint('✅ 实时通话录音已停止');
    } catch (e) {
      debugPrint('❌ 停止实时通话录音失败: $e');
    }
  }

  /// 完全停止并释放资源（用于挂断通话）
  Future<void> dispose() async {
    try {
      debugPrint('🎤 释放实时通话录音资源');

      _isRecording = false;
      _isVoiceTransmitting = false;

      // 停止录音流
      await _recordStreamSub?.cancel();
      _recordStreamSub = null;
      await _recorder.dispose();

      // 清空缓冲区
      _pcmBuffer.clear();
      _encodedFrameBuffer.clear();

      // 释放编解码器
      await _codec?.dispose();
      _codec = null;

      // 重置VAD状态
      _vad.reset();

      // 恢复正常音频模式
      await CallAudioSessionManager.instance.restoreNormalMode();

      debugPrint('✅ 实时通话录音资源已释放，音频模式已恢复');
    } catch (e) {
      debugPrint('❌ 释放实时通话录音资源失败: $e');
    }
  }

  /// 发送编码后的语音帧
  Future<void> _sendEncodedFrames() async {
    if (_encodedFrameBuffer.isEmpty) return;

    try {
      debugPrint('📦 打包发送 ${_encodedFrameBuffer.length} 帧语音数据');

      // 使用多帧组装器打包数据
      final assembler = MultiFrameVoiceAssembler.instance;
      final packedData = assembler.packFrames(_encodedFrameBuffer);

      debugPrint('📦 打包完成: ${packedData.length}字节');

      // 发送语音数据
      await _trySendVoice(packedData);

      // 清空已发送的帧
      _encodedFrameBuffer.clear();
    } catch (e) {
      debugPrint('❌ 发送语音帧失败: $e');
    }
  }

  /// 尝试发送语音数据
  Future<void> _trySendVoice(Uint8List encodedData) async {
    final device = BluetoothManager.currentDevice.value;
    if (device == null) {
      debugPrint('⚠️ 未连接设备，无法发送语音数据');
      return;
    }

    try {
      debugPrint('📤 发送实时语音数据: ${encodedData.length}字节, srcId=0x${_currentSrcId.toRadixString(16)}');
      await TK8620RequestSender.sendRealTimeVoiceData(
        device,
        audioData: encodedData,
        srcId: _currentSrcId,
        isLastPacket: false,
      );
      debugPrint('✅ 实时语音数据发送成功');
    } catch (e) {
      debugPrint('❌ 发送实时语音数据失败: $e');
    }
  }

  /// 将原始字节数据转换为Int16List
  Int16List _convertBytesToInt16List(Uint8List bytes) {
    final Int16List result = Int16List(bytes.length ~/ 2);
    for (int i = 0; i < result.length; i++) {
      // 小端字节序 (Little Endian)
      result[i] = bytes[i * 2] | (bytes[i * 2 + 1] << 8);
    }
    return result;
  }

  /// 检查是否正在录音
  bool get isRecording => _isRecording;

  /// 检查是否正在传输语音
  bool get isVoiceTransmitting => _isVoiceTransmitting;

  /// 获取VAD状态
  bool get isVoiceActive => _vad.isVoiceActive;
}
