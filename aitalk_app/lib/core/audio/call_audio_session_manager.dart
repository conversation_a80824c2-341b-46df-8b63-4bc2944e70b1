import 'dart:async';

import 'package:flutter/material.dart';
import 'package:audio_session/audio_session.dart';
import '../services/settings_service.dart';

/// 通话音频会话管理器
///
/// 负责管理实时通话时的音频会话配置，包括：
/// - 强制使用听筒模式（避免外放造成的麦克风反馈）
/// - 配置适合通话的音频参数
/// - 处理音频中断和设备变化
class CallAudioSessionManager {
  CallAudioSessionManager._();

  static final CallAudioSessionManager instance = CallAudioSessionManager._();

  AudioSession? _audioSession;
  bool _isCallModeActive = false;
  bool _isSpeakerEnabled = false; // 当前是否启用扬声器模式
  StreamSubscription? _interruptionSubscription;
  StreamSubscription? _becomingNoisySubscription;

  /// 配置通话模式的音频会话
  ///
  /// 这将：
  /// 1. 根据用户设置决定是否强制使用听筒模式
  /// 2. 启用录音和播放功能
  /// 3. 优化语音通话的音频质量
  /// 4. 减少麦克风反馈和回音
  Future<bool> configureForCall() async {
    try {
      debugPrint('🎧 配置通话模式音频会话');

      _audioSession = await AudioSession.instance;

      // 获取用户设置
      final enableEchoCancellation =
          await SettingsService.getCallEnableEchoCancellation() ?? true;
      final enableNoiseSuppression =
          await SettingsService.getCallEnableNoiseSuppression() ?? true;

      debugPrint(
        '🎧 用户设置 - 回音消除: $enableEchoCancellation, 噪音抑制: $enableNoiseSuppression',
      );

      // 默认使用听筒模式，用户可以通过界面按钮切换
      _isSpeakerEnabled = false;
      AVAudioSessionCategoryOptions categoryOptions =
          AVAudioSessionCategoryOptions.none; // 听筒模式

      // 配置专门用于通话的音频会话
      await _audioSession!.configure(
        AudioSessionConfiguration(
          // iOS 配置
          avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
          avAudioSessionCategoryOptions: categoryOptions,
          avAudioSessionMode: AVAudioSessionMode.voiceChat, // 语音聊天模式，优化通话质量
          avAudioSessionRouteSharingPolicy:
              AVAudioSessionRouteSharingPolicy.defaultPolicy,
          avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,

          // Android 配置
          androidAudioAttributes: const AndroidAudioAttributes(
            contentType: AndroidAudioContentType.speech, // 语音内容
            flags: AndroidAudioFlags.none,
            usage: AndroidAudioUsage.voiceCommunication, // 语音通信用途
          ),
          androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
          androidWillPauseWhenDucked: false, // 通话时不暂停
        ),
      );

      // 激活音频会话
      final activated = await _audioSession!.setActive(true);
      if (!activated) {
        debugPrint('❌ 音频会话激活失败');
        return false;
      }

      _isCallModeActive = true;

      // 监听音频中断
      _setupInterruptionHandling();

      final modeDesc = _isSpeakerEnabled ? '扬声器模式' : '听筒模式';
      debugPrint('✅ 通话模式音频会话配置成功 ($modeDesc)');
      return true;
    } catch (e) {
      debugPrint('❌ 配置通话模式音频会话失败: $e');
      return false;
    }
  }

  /// 恢复正常模式的音频会话
  Future<void> restoreNormalMode() async {
    try {
      debugPrint('🎧 恢复正常模式音频会话');

      if (_audioSession == null) return;

      // 取消监听
      await _interruptionSubscription?.cancel();
      await _becomingNoisySubscription?.cancel();
      _interruptionSubscription = null;
      _becomingNoisySubscription = null;

      // 配置正常的音频会话（用于音乐播放等）
      await _audioSession!.configure(AudioSessionConfiguration.music());

      // 停用音频会话
      await _audioSession!.setActive(false);

      _isCallModeActive = false;

      debugPrint('✅ 正常模式音频会话恢复成功');
    } catch (e) {
      debugPrint('❌ 恢复正常模式音频会话失败: $e');
    }
  }

  /// 设置音频中断处理
  void _setupInterruptionHandling() {
    if (_audioSession == null) return;

    // 监听音频中断（如来电）
    _interruptionSubscription = _audioSession!.interruptionEventStream.listen((
      event,
    ) {
      debugPrint('🎧 音频中断事件: ${event.type}, begin: ${event.begin}');

      if (event.begin) {
        switch (event.type) {
          case AudioInterruptionType.duck:
            // 其他应用开始播放音频，我们应该降低音量
            debugPrint('🎧 音频被要求降低音量');
            break;
          case AudioInterruptionType.pause:
          case AudioInterruptionType.unknown:
            // 其他应用开始播放音频，我们应该暂停
            debugPrint('🎧 音频被要求暂停');
            break;
        }
      } else {
        switch (event.type) {
          case AudioInterruptionType.duck:
            // 中断结束，恢复正常音量
            debugPrint('🎧 音频中断结束，恢复音量');
            break;
          case AudioInterruptionType.pause:
            // 中断结束，可以恢复播放
            debugPrint('🎧 音频中断结束，可以恢复播放');
            break;
          case AudioInterruptionType.unknown:
            // 中断结束但不应该自动恢复
            debugPrint('🎧 音频中断结束，但不自动恢复');
            break;
        }
      }
    });

    // 监听耳机拔出事件
    _becomingNoisySubscription = _audioSession!.becomingNoisyEventStream.listen(
      (_) {
        debugPrint('🎧 检测到耳机拔出，应该暂停通话或降低音量');
        // 这里可以触发通话暂停或音量调整的回调
      },
    );
  }

  /// 检查是否处于通话模式
  bool get isCallModeActive => _isCallModeActive;

  /// 获取当前音频会话
  AudioSession? get audioSession => _audioSession;

  /// 获取当前是否启用扬声器模式
  bool get isSpeakerEnabled => _isSpeakerEnabled;

  /// 切换扬声器模式
  ///
  /// [enabled] true: 启用扬声器模式（外放）
  /// [enabled] false: 启用听筒模式（贴耳）
  Future<bool> setSpeakerEnabled(bool enabled) async {
    if (!_isCallModeActive || _audioSession == null) {
      debugPrint('❌ 通话模式未激活，无法切换扬声器模式');
      return false;
    }

    try {
      debugPrint('🔊 切换扬声器模式: ${enabled ? "启用" : "禁用"}');

      // 根据扬声器状态配置音频会话选项
      AVAudioSessionCategoryOptions categoryOptions;
      if (enabled) {
        // 启用扬声器模式：允许蓝牙和默认使用扬声器
        categoryOptions =
            AVAudioSessionCategoryOptions.allowBluetooth |
            AVAudioSessionCategoryOptions.defaultToSpeaker;
      } else {
        // 启用听筒模式：不设置任何选项，强制使用听筒
        categoryOptions = AVAudioSessionCategoryOptions.none;
      }

      // 重新配置音频会话
      await _audioSession!.configure(
        AudioSessionConfiguration(
          // iOS 配置
          avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
          avAudioSessionCategoryOptions: categoryOptions,
          avAudioSessionMode: AVAudioSessionMode.voiceChat, // 语音聊天模式，优化通话质量
          avAudioSessionRouteSharingPolicy:
              AVAudioSessionRouteSharingPolicy.defaultPolicy,
          avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,

          // Android 配置
          androidAudioAttributes: const AndroidAudioAttributes(
            contentType: AndroidAudioContentType.speech, // 语音内容
            flags: AndroidAudioFlags.none,
            usage: AndroidAudioUsage.voiceCommunication, // 语音通信用途
          ),
          androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
          androidWillPauseWhenDucked: false, // 通话时不暂停
        ),
      );

      _isSpeakerEnabled = enabled;

      final modeDesc = enabled ? '扬声器模式' : '听筒模式';
      debugPrint('✅ 音频输出模式切换成功: $modeDesc');
      return true;
    } catch (e) {
      debugPrint('❌ 切换扬声器模式失败: $e');
      return false;
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    await restoreNormalMode();
    _audioSession = null;
  }
}
