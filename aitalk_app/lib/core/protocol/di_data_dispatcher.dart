import 'dart:async';
import 'dart:typed_data';

import 'at_response_handler.dart';
import 'binary_escape.dart';
import 'tk8620_frame_decoder.dart';
import 'text_packet_assembler.dart';
import 'voice_packet_assembler.dart';
import 'tk8620_protocol.dart';
import 'package:flutter/foundation.dart'; // debugPrint
import '../audio/voice_wav_saver.dart';
import '../audio/voice_player.dart';
import '../audio/multi_frame_voice_assembler.dart';
import '../services/settings_service.dart';
import 'join_response_processor.dart';
import '../services/database_service.dart'; // 新增导入
import '../bluetooth/bluetooth_manager.dart'; // 修正导入
import '../utils/group_util.dart'; // 新增导入
import 'tk8620_request_sender.dart'; // 修正导入
import 'tk8620_protocol.dart'; // TK8620SessionIdGenerator 定义于此
import 'session_join_handler.dart'; // 新增导入
import 'voice_call_handler.dart'; // 新增导入
// 不再单独创建解码器，直接复用 VoicePlayer.playFrame 的返回结果

/// DI 数据统一分发器
///
/// 职责:
///   1. 监听 `AtResponseHandler` 的广播流;
///   2. 仅处理 `AtResponseType.diData` 响应, 提取未转义字节;
///   3. 进行 TK8620 帧解码, 解码成功后通过 `frames` Stream 广播;
///
///   *不* 关心后续业务载荷解析, 上层可使用 `TK8620PayloadParser` 自行处理。
class DiDataDispatcher {
  DiDataDispatcher._() {
    // 初始化远端语音播放器
    VoicePlayer.create().then((vp) => _voicePlayer = vp);
    // 读取用户设置的自动播放偏好
    SettingsService.loadAutoPlayVoice().then((value) {
      if (value != null) _autoPlayVoice = value;
    });
    _sub = AtResponseHandler.instance.responses.listen(_onAtResponse);
  }

  static final DiDataDispatcher instance = DiDataDispatcher._();

  late final StreamSubscription<AtResponse> _sub;

  final StreamController<TK8620Frame> _frameController =
      StreamController<TK8620Frame>.broadcast();

  // 解析后的业务对象流
  final StreamController<TK8620Message> _messageController =
      StreamController<TK8620Message>.broadcast();

  // 文本包组装器
  final TextPacketAssembler _textAssembler = TextPacketAssembler();
  // 语音包组装器
  final VoicePacketAssembler _voiceAssembler = VoicePacketAssembler();

  // 语音播放器实例（收到语音帧时即时解码播放）
  VoicePlayer? _voicePlayer;

  // 是否自动播放远端语音，默认开启
  bool _autoPlayVoice = true;

  /// 供外部(设置页)调用，以实时更新偏好
  void setAutoPlayVoice(bool enabled) {
    _autoPlayVoice = enabled;
  }

  // 👉 PCM 缓存/保存逻辑已移至 VoiceWavSaver

  /// 已解析出的 TK8620 帧流
  Stream<TK8620Frame> get frames => _frameController.stream;

  /// 解析后的业务消息流 (高层直接使用此流，无需关注底层协议细节)
  Stream<TK8620Message> get messages => _messageController.stream;

  void _onAtResponse(AtResponse res) {
    if (res.type != AtResponseType.diData) return;

    final raw = res.payload?['raw'];
    if (raw is! Uint8List || raw.isEmpty) {
      debugPrint('[DiDataDispatcher] 无效的原始数据');
      return;
    }

    // 此处 raw 已经是 BinaryEscape.unescape 的结果, 但为了稳妥再确保一次
    final Uint8List unescaped = raw;
    debugPrint('[DiDataDispatcher] 收到DI数据，长度: ${unescaped.length}');
    debugPrint(
      '[DiDataDispatcher] 原始数据: ${unescaped.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
    );

    final frame = TK8620FrameDecoder.tryDecode(unescaped);
    if (frame != null) {
      debugPrint(
        '[DiDataDispatcher] 成功解码TK8620帧: ${frame.frameType}, src=${frame.srcId}, dst=0x${frame.dstId.toRadixString(16).padLeft(8, '0')}',
      );
      debugPrint(
        '[DiDataDispatcher] 载荷长度: ${frame.payload.length}, 载荷内容: ${frame.payload.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );
      _frameController.add(frame);

      // 进一步解析帧载荷并分发业务消息
      _handleFrame(frame);
    } else {
      debugPrint('[DiDataDispatcher] TK8620帧解码失败');
    }
  }

  /// 根据帧类型解析载荷并生成业务消息
  void _handleFrame(TK8620Frame frame) {
    if (frame.payload.isEmpty) return;

    dynamic parsed;

    switch (frame.frameType) {
      case TK8620FrameType.session:
        final ctrl = frame.payload[0];
        switch (ctrl) {
          case TK8620SessionCode.joinRequest:
            parsed = TK8620PayloadParser.parseJoinRequest(frame.payload);
            if (parsed is TK8620SessionJoinRequest) {
              // 异步处理密码校验与响应
              unawaited(SessionJoinHandler.handle(frame, parsed));
            }
            break;
          case TK8620SessionCode.joinResponse:
            parsed = TK8620PayloadParser.parseJoinResponse(frame.payload);

            if (parsed is TK8620SessionJoinResponse && parsed.isSuccess) {
              // 取 groupId (即 SessionID 的8位16进制形式)
              final String groupId = parsed.sessionId
                  .toRadixString(16)
                  .padLeft(8, '0')
                  .toUpperCase();

              unawaited(JoinResponseProcessor.handle(groupId, parsed));
            }
            break;
          case TK8620SessionCode.createTalkRequest:
            parsed = TK8620PayloadParser.parseCreateTalkRequest(frame.payload);
            if (parsed is TK8620CreateTalkRequest) {
              // 处理建立通话请求
              debugPrint('📞 收到建立通话请求: ${parsed.toString()}');
              unawaited(
                VoiceCallHandler.handleCreateTalkRequest(frame, parsed),
              );
            }
            break;
          case TK8620SessionCode.createTalkResponse:
            parsed = TK8620PayloadParser.parseCreateTalkResponse(frame.payload);
            if (parsed is TK8620CreateTalkResponse) {
              // 处理建立通话响应
              debugPrint('📞 收到建立通话响应: ${parsed.toString()}');
              unawaited(
                VoiceCallHandler.handleCreateTalkResponse(frame, parsed),
              );
            }
            break;
          // 其他 SessionCode 可在此扩展

          default:
            // ⚠️ 未识别的会话控制码
            debugPrint(
              '⚠️ 未识别的 Session 控制码: 0x${ctrl.toRadixString(16).padLeft(2, '0')}',
            );
            break;
        }
        break;

      case TK8620FrameType.data:
        final dataType = frame.payload[0];
        switch (dataType) {
          case TK8620DataType.text:
            parsed = _textAssembler.handleTextFrame(frame);
            break;
          case TK8620DataType.gps:
            parsed = TK8620PayloadParser.parseGPSData(frame.payload);
            break;
          case TK8620DataType.voice:
            parsed = _voiceAssembler.handleVoiceFrame(frame);
            break;
          // 图片、文件等后续补充

          default:
            // ⚠️ 未识别的数据类型码
            debugPrint(
              '⚠️ 未识别的数据类型码: 0x${dataType.toRadixString(16).padLeft(2, '0')}',
            );
            break;
        }
        break;

      case TK8620FrameType.voice:
        // 语音帧: 使用组装器，等待完整包
        parsed = _voiceAssembler.handleVoiceFrame(frame);
        break;

      case TK8620FrameType.command:
        // TODO: 根据具体命令码解析

        // 如果未实现，打印提示，方便后续补充
        debugPrint('⚠️ 未实现的命令帧解析');
        break;

      default:
        // 理论上不会走到此分支，防御性代码
        debugPrint('⚠️ 未识别的帧类型: ${frame.frameType}');
        break;
    }

    if (parsed != null) {
      debugPrint('[DiDataDispatcher] 生成业务消息: ${parsed.runtimeType}');
      _messageController.add(TK8620Message(frame: frame, payload: parsed));
    } else {
      debugPrint('[DiDataDispatcher] 载荷解析失败，未生成业务消息');
    }

    // 👂 语音播放：区分PTT语音和实时通话语音
    if (frame.frameType == TK8620FrameType.voice ||
        (frame.frameType == TK8620FrameType.data &&
            frame.payload.isNotEmpty &&
            frame.payload[0] == TK8620DataType.voice)) {
      // PTT语音：保存为WAV文件
      final vd = TK8620PayloadParser.parseVoiceData(frame.payload);
      if (vd != null) {
        // 检测是否为多帧数据包（通过数据长度和格式判断）
        if (_isMultiFramePacket(vd.audioData)) {
          // 处理多帧数据包
          _voicePlayer
              ?.playMultiFramePacket(vd.audioData, playAudio: _autoPlayVoice)
              .then((List<Int16List> pcmFrames) async {
                // 将多帧PCM数据逐帧保存
                for (int i = 0; i < pcmFrames.length; i++) {
                  final isLastFrame =
                      vd.isLastPacket && (i == pcmFrames.length - 1);
                  await VoiceWavSaver.instance.handleVoice(
                    srcId: frame.srcId,
                    pcm: pcmFrames[i],
                    isLastPacket: isLastFrame,
                    isMine: false,
                  );
                }
              });
        } else {
          // 处理单帧数据包（兼容旧格式）
          _voicePlayer?.playFrame(vd.audioData, playAudio: _autoPlayVoice).then(
            (Int16List? pcm) async {
              if (pcm != null) {
                await VoiceWavSaver.instance.handleVoice(
                  srcId: frame.srcId,
                  pcm: pcm,
                  isLastPacket: vd.isLastPacket,
                  isMine: false,
                );
              }
            },
          );
        }
      }
    } else if (frame.frameType == TK8620FrameType.data &&
        frame.payload.isNotEmpty &&
        frame.payload[0] == TK8620DataType.realTimeVoice) {
      // 实时通话语音：只播放，不保存WAV文件
      final rtVd = TK8620PayloadParser.parseRealTimeVoiceData(frame.payload);
      if (rtVd != null) {
        debugPrint('🔊 收到实时通话语音: ${rtVd.audioData.length}字节');
        _voicePlayer?.playFrame(rtVd.audioData, playAudio: _autoPlayVoice);
        // 注意：实时通话语音不保存为WAV文件
      }
    }
  }

  /// 检测是否为多帧数据包
  ///
  /// 多帧数据包的特征：
  /// 1. 第一个字节是帧数量（1-5）
  /// 2. 后续数据长度应该等于 帧数量 * 11字节
  bool _isMultiFramePacket(Uint8List audioData) {
    if (audioData.isEmpty) return false;

    // 检查第一个字节是否为合理的帧数量
    final frameCount = audioData[0];
    if (frameCount < 1 ||
        frameCount > MultiFrameVoiceAssembler.framesPerPacket) {
      return false;
    }

    // 检查数据长度是否匹配多帧格式
    final expectedLength =
        1 + frameCount * MultiFrameVoiceAssembler.melpFrameSize;
    if (audioData.length != expectedLength) {
      return false;
    }

    // 如果数据长度正好等于单个MELP帧的大小，则认为是单帧
    if (audioData.length == MultiFrameVoiceAssembler.melpFrameSize) {
      return false;
    }

    return true;
  }

  // _handleSuccessfulJoin 移至 JoinResponseProcessor
}

/// 封装了原始帧及解析后载荷的业务消息对象
class TK8620Message {
  TK8620Message({required this.frame, required this.payload});

  final TK8620Frame frame;
  final dynamic payload; // 具体类型取决于帧内容, 例如 TK8620TextData / TK8620GPSData 等

  @override
  String toString() => 'TK8620Message(${frame.frameType}, payload: $payload)';
}
