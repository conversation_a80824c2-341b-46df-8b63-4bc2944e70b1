import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter/material.dart';

/// 语音活动检测器（Voice Activity Detector）
/// 
/// 用于检测音频帧中是否包含有效的语音信号，避免传输静音或环境噪音
class VoiceActivityDetector {
  VoiceActivityDetector._();
  
  static final VoiceActivityDetector instance = VoiceActivityDetector._();
  
  // VAD 参数配置
  static const double _energyThreshold = 1000.0; // 能量阈值
  static const double _zeroCrossingThreshold = 0.1; // 过零率阈值
  static const int _minVoiceFrames = 3; // 连续语音帧数阈值
  static const int _minSilenceFrames = 5; // 连续静音帧数阈值
  static const int _hangoverFrames = 3; // 语音结束后的延续帧数
  
  // 状态变量
  int _consecutiveVoiceFrames = 0;
  int _consecutiveSilenceFrames = 0;
  int _hangoverCounter = 0;
  bool _isVoiceActive = false;
  
  // 自适应阈值相关
  double _backgroundNoise = 100.0;
  double _adaptiveEnergyThreshold = _energyThreshold;
  final List<double> _recentEnergies = [];
  static const int _energyHistorySize = 50;
  
  /// 检测音频帧是否包含语音活动
  /// 
  /// [pcmFrame] PCM音频帧数据
  /// 返回true表示检测到语音活动，false表示静音
  bool detectVoiceActivity(Int16List pcmFrame) {
    // 计算音频特征
    final energy = _calculateEnergy(pcmFrame);
    final zeroCrossingRate = _calculateZeroCrossingRate(pcmFrame);
    
    // 更新自适应阈值
    _updateAdaptiveThreshold(energy);
    
    // 基于能量和过零率的语音检测
    final hasVoiceFeatures = energy > _adaptiveEnergyThreshold && 
                            zeroCrossingRate > _zeroCrossingThreshold;
    
    // 状态机逻辑
    if (hasVoiceFeatures) {
      _consecutiveVoiceFrames++;
      _consecutiveSilenceFrames = 0;
      _hangoverCounter = 0;
      
      // 连续语音帧数达到阈值，激活语音状态
      if (_consecutiveVoiceFrames >= _minVoiceFrames) {
        _isVoiceActive = true;
      }
    } else {
      _consecutiveVoiceFrames = 0;
      
      if (_isVoiceActive) {
        // 语音活动中，开始hangover计数
        if (_hangoverCounter < _hangoverFrames) {
          _hangoverCounter++;
          // 在hangover期间仍然认为是语音活动
        } else {
          // hangover结束，开始计算静音帧
          _consecutiveSilenceFrames++;
          if (_consecutiveSilenceFrames >= _minSilenceFrames) {
            _isVoiceActive = false;
            _consecutiveSilenceFrames = 0;
            _hangoverCounter = 0;
          }
        }
      }
    }
    
    // 调试信息（仅在debug模式下）
    if (kDebugMode && (_consecutiveVoiceFrames % 10 == 0 || _consecutiveSilenceFrames % 10 == 0)) {
      debugPrint('🎤 VAD: 能量=${energy.toStringAsFixed(1)}, '
                '阈值=${_adaptiveEnergyThreshold.toStringAsFixed(1)}, '
                '过零率=${zeroCrossingRate.toStringAsFixed(3)}, '
                '语音=${_isVoiceActive ? "是" : "否"}');
    }
    
    return _isVoiceActive;
  }
  
  /// 计算音频帧的能量
  double _calculateEnergy(Int16List pcmFrame) {
    double energy = 0.0;
    for (int sample in pcmFrame) {
      energy += sample * sample;
    }
    return energy / pcmFrame.length;
  }
  
  /// 计算过零率（Zero Crossing Rate）
  double _calculateZeroCrossingRate(Int16List pcmFrame) {
    int zeroCrossings = 0;
    for (int i = 1; i < pcmFrame.length; i++) {
      if ((pcmFrame[i] >= 0) != (pcmFrame[i - 1] >= 0)) {
        zeroCrossings++;
      }
    }
    return zeroCrossings / (pcmFrame.length - 1);
  }
  
  /// 更新自适应能量阈值
  void _updateAdaptiveThreshold(double energy) {
    _recentEnergies.add(energy);
    
    // 保持历史能量数据在指定大小内
    if (_recentEnergies.length > _energyHistorySize) {
      _recentEnergies.removeAt(0);
    }
    
    // 计算背景噪音水平（使用最小值的平均）
    if (_recentEnergies.length >= 10) {
      final sortedEnergies = List<double>.from(_recentEnergies)..sort();
      final backgroundSamples = sortedEnergies.take(sortedEnergies.length ~/ 4).toList();
      _backgroundNoise = backgroundSamples.reduce((a, b) => a + b) / backgroundSamples.length;
      
      // 自适应阈值 = 背景噪音 * 倍数 + 基础阈值
      _adaptiveEnergyThreshold = math.max(
        _backgroundNoise * 3.0 + 200.0,
        _energyThreshold * 0.5, // 最小阈值
      );
    }
  }
  
  /// 重置VAD状态
  void reset() {
    _consecutiveVoiceFrames = 0;
    _consecutiveSilenceFrames = 0;
    _hangoverCounter = 0;
    _isVoiceActive = false;
    _backgroundNoise = 100.0;
    _adaptiveEnergyThreshold = _energyThreshold;
    _recentEnergies.clear();
    debugPrint('🎤 VAD状态已重置');
  }
  
  /// 获取当前语音活动状态
  bool get isVoiceActive => _isVoiceActive;
  
  /// 获取当前自适应阈值
  double get currentThreshold => _adaptiveEnergyThreshold;
  
  /// 获取背景噪音水平
  double get backgroundNoiseLevel => _backgroundNoise;
  
  /// 手动设置敏感度（调整基础阈值）
  /// [sensitivity] 敏感度，范围0.1-2.0，值越小越敏感
  void setSensitivity(double sensitivity) {
    sensitivity = sensitivity.clamp(0.1, 2.0);
    _adaptiveEnergyThreshold = _energyThreshold * sensitivity;
    debugPrint('🎤 VAD敏感度已设置为: $sensitivity');
  }
}
